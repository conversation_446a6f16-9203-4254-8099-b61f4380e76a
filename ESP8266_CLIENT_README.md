# ESP8266 TCP客户端功能

本功能允许ESP32设备定期连接到ESP8266设备，实现设备间的通信。

## 功能特点

- 自动在启动时初始化ESP8266客户端
- 定期切换WiFi连接到ESP8266 AP
- 发送数据到ESP8266 TCP服务器
- 接收ESP8266的响应数据
- 自动恢复原始WiFi连接

## 配置方法

### 1. 启用ESP8266客户端功能

在项目配置中启用ESP8266客户端：

```bash
idf.py menuconfig
```

导航到 `Xiaozhi Assistant` -> `启用ESP8266 TCP客户端连接`，选择 `Y` 启用。

### 2. 配置ESP8266连接参数

在同一菜单中配置以下参数：

- **ESP8266 AP SSID**: ESP8266设备的WiFi热点名称（默认：ESP8266_AP）
- **ESP8266 AP Password**: ESP8266设备的WiFi密码（默认：password123）
- **ESP8266 Server IP**: ESP8266 TCP服务器的IP地址（默认：***********）
- **ESP8266 Server Port**: ESP8266 TCP服务器的端口号（默认：8080）

## ESP8266端代码

以下是ESP8266端需要运行的TCP服务器代码：

```cpp
#include <ESP8266WiFi.h>
#include <WiFiServer.h>

const char* ssid = "ESP8266_AP";
const char* password = "password123";

WiFiServer server(8080);

void setup() {
  Serial.begin(115200);
  
  // 设置为AP模式
  WiFi.mode(WIFI_AP);
  WiFi.softAP(ssid, password);
  
  Serial.print("AP IP address: ");
  Serial.println(WiFi.softAPIP());
  
  server.begin();
  Serial.println("TCP Server started on port 8080");
}

void loop() {
  WiFiClient client = server.available();
  
  if (client) {
    Serial.println("Client connected");
    
    while (client.connected()) {
      if (client.available()) {
        String data = client.readString();
        Serial.println("Received: " + data);
        
        // 发送响应
        client.println("Hello from ESP8266! Received: " + data);
      }
      delay(10);
    }
    
    client.stop();
    Serial.println("Client disconnected");
  }
}
```

## 工作原理

1. **启动时初始化**: 设备启动后，ESP8266客户端会自动初始化
2. **定期连接**: 每60秒（可配置），客户端会：
   - 保存当前WiFi配置
   - 切换连接到ESP8266 AP
   - 建立TCP连接并发送数据
   - 接收ESP8266的响应
   - 恢复原始WiFi连接
3. **数据处理**: 接收到的数据会通过回调函数处理

## 使用示例

在应用程序中，ESP8266客户端会自动启动。你可以通过日志查看连接状态：

```
I (12345) ESP8266_CLIENT: Starting periodic ESP8266 connection every 60 seconds
I (12346) ESP8266_CLIENT: Starting periodic connection to ESP8266
I (12347) ESP8266_CLIENT: Saved current WiFi config: YourHomeWiFi
I (15678) ESP8266_CLIENT: Connected to ESP8266 AP successfully
I (15679) ESP8266_CLIENT: TCP connection established successfully
I (15680) ESP8266_CLIENT: Sent data: Periodic hello from ESP32!
I (15681) ESP8266_CLIENT: Received data: Hello from ESP8266! Received: Periodic hello from ESP32!
I (20681) ESP8266_CLIENT: Restoring original WiFi connection: YourHomeWiFi
```

## 注意事项

1. **WiFi切换**: 此功能会临时切换WiFi连接，可能会短暂中断网络服务
2. **连接间隔**: 建议设置合理的连接间隔，避免频繁切换影响主要功能
3. **ESP8266配置**: 确保ESP8266设备正确配置为AP模式并运行TCP服务器
4. **网络稳定性**: 在网络环境不稳定时，可能需要调整超时和重试参数

## 自定义配置

如果需要修改连接间隔，可以在代码中调用：

```cpp
esp8266_client.StartPeriodicConnection(120); // 每120秒连接一次
```
