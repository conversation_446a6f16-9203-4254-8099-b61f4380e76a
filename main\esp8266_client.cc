#include "esp8266_client.h"
#include <esp_netif.h>
#include <nvs_flash.h>
#include <lwip/err.h>
#include <lwip/sys.h>
#include <lwip/netdb.h>
#include <lwip/dns.h>
#include <arpa/inet.h>
#include <cstring>
#include <esp_err.h>

#ifdef CONFIG_USE_ESP8266_CLIENT

const char* Esp8266Client::TAG = "ESP8266_CLIENT";

Esp8266Client::Esp8266Client()
    : tcp_task_handle_(nullptr)
    , periodic_task_handle_(nullptr)
    , wifi_event_group_(nullptr)
    , initialized_(false)
    , connected_(false)
    , should_stop_(false)
    , periodic_mode_(false)
    , socket_fd_(-1)
    , periodic_interval_(60)
    , server_port_(8080)
    , has_original_config_(false)
{
#ifdef CONFIG_USE_ESP8266_CLIENT
    esp8266_ssid_ = CONFIG_ESP8266_SSID;
    esp8266_password_ = CONFIG_ESP8266_PASSWORD;
    server_ip_ = CONFIG_ESP8266_SERVER_IP;
    server_port_ = CONFIG_ESP8266_SERVER_PORT;
#else
    esp8266_ssid_ = "ESP8266_AP";
    esp8266_password_ = "password123";
    server_ip_ = "***********";
    server_port_ = 8080;
#endif
}

Esp8266Client::~Esp8266Client() {
    Stop();
    if (wifi_event_group_) {
        vEventGroupDelete(wifi_event_group_);
    }
}

void Esp8266Client::Initialize() {
    if (initialized_) {
        return;
    }

    ESP_LOGI(TAG, "Initializing ESP8266 client");

    // 创建事件组
    wifi_event_group_ = xEventGroupCreate();

    // 注册WiFi事件处理器
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID,
                                               &wifi_event_handler, this));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP,
                                               &wifi_event_handler, this));

    initialized_ = true;
    ESP_LOGI(TAG, "ESP8266 client initialized");
}

void Esp8266Client::Start() {
    if (!initialized_) {
        Initialize();
    }
    
    ESP_LOGI(TAG, "Starting ESP8266 client connection");
    should_stop_ = false;
    
    // 连接到ESP8266 AP
    if (ConnectToEsp8266()) {
        ESP_LOGI(TAG, "ESP8266 client started successfully");
    } else {
        ESP_LOGE(TAG, "Failed to start ESP8266 client");
    }
}

void Esp8266Client::Stop() {
    ESP_LOGI(TAG, "Stopping ESP8266 client");
    should_stop_ = true;
    
    // 关闭socket
    if (socket_fd_ >= 0) {
        close(socket_fd_);
        socket_fd_ = -1;
    }
    
    // 停止TCP任务
    if (tcp_task_handle_) {
        vTaskDelete(tcp_task_handle_);
        tcp_task_handle_ = nullptr;
    }
    
    // 断开WiFi连接
    esp_wifi_disconnect();
    esp_wifi_stop();
    
    connected_ = false;
    
    // 通知连接状态变化
    if (connection_state_callback_) {
        connection_state_callback_(false);
    }
    
    ESP_LOGI(TAG, "ESP8266 client stopped");
}

bool Esp8266Client::IsConnected() const {
    return connected_;
}

bool Esp8266Client::SendData(const std::string& data) {
    if (!connected_ || socket_fd_ < 0) {
        ESP_LOGW(TAG, "Not connected, cannot send data");
        return false;
    }
    
    int bytes_sent = send(socket_fd_, data.c_str(), data.length(), 0);
    if (bytes_sent < 0) {
        ESP_LOGE(TAG, "Failed to send data: %d", errno);
        return false;
    }
    
    ESP_LOGI(TAG, "Sent data: %s", data.c_str());
    return true;
}

void Esp8266Client::OnDataReceived(std::function<void(const std::string&)> callback) {
    data_received_callback_ = callback;
}

void Esp8266Client::OnConnectionStateChanged(std::function<void(bool)> callback) {
    connection_state_callback_ = callback;
}

void Esp8266Client::wifi_event_handler(void* arg, esp_event_base_t event_base,
                                       int32_t event_id, void* event_data) {
    Esp8266Client* client = static_cast<Esp8266Client*>(arg);
    
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
        ESP_LOGI(TAG, "WiFi connection started");
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        if (!client->should_stop_) {
            ESP_LOGI(TAG, "WiFi disconnected, retrying...");
            esp_wifi_connect();
        }
        xEventGroupSetBits(client->wifi_event_group_, WIFI_FAIL_BIT);
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP address: " IPSTR, IP2STR(&event->ip_info.ip));
        xEventGroupSetBits(client->wifi_event_group_, WIFI_CONNECTED_BIT);
        
        // 创建TCP客户端任务
        xTaskCreate(tcp_client_task, "tcp_client_task", 4096, client, 5, &client->tcp_task_handle_);
    }
}

bool Esp8266Client::ConnectToEsp8266() {
    ESP_LOGI(TAG, "Connecting to ESP8266 AP: %s", esp8266_ssid_.c_str());

    // 配置WiFi连接到ESP8266 AP
    wifi_config_t wifi_config = {};
    strncpy((char*)wifi_config.sta.ssid, esp8266_ssid_.c_str(), sizeof(wifi_config.sta.ssid) - 1);
    strncpy((char*)wifi_config.sta.password, esp8266_password_.c_str(), sizeof(wifi_config.sta.password) - 1);
    wifi_config.sta.threshold.authmode = WIFI_AUTH_WPA2_PSK;

    // 设置WiFi模式为STA并配置
    esp_err_t ret = esp_wifi_set_mode(WIFI_MODE_STA);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set WiFi mode: %s", esp_err_to_name(ret));
        return false;
    }

    ret = esp_wifi_set_config(WIFI_IF_STA, &wifi_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set WiFi config: %s", esp_err_to_name(ret));
        return false;
    }

    ret = esp_wifi_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start WiFi: %s", esp_err_to_name(ret));
        return false;
    }

    // 等待连接结果，设置超时时间为30秒
    EventBits_t bits = xEventGroupWaitBits(wifi_event_group_,
                                           WIFI_CONNECTED_BIT | WIFI_FAIL_BIT,
                                           pdFALSE,
                                           pdFALSE,
                                           pdMS_TO_TICKS(30000));

    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "Connected to ESP8266 AP successfully");
        return true;
    } else if (bits & WIFI_FAIL_BIT) {
        ESP_LOGE(TAG, "Failed to connect to ESP8266 AP");
        return false;
    } else {
        ESP_LOGE(TAG, "Connection to ESP8266 AP timed out");
        return false;
    }
}

bool Esp8266Client::CreateTcpConnection() {
    struct sockaddr_in dest_addr;
    dest_addr.sin_addr.s_addr = inet_addr(server_ip_.c_str());
    dest_addr.sin_family = AF_INET;
    dest_addr.sin_port = htons(server_port_);

    socket_fd_ = socket(AF_INET, SOCK_STREAM, IPPROTO_IP);
    if (socket_fd_ < 0) {
        ESP_LOGE(TAG, "Failed to create socket: %d", errno);
        return false;
    }

    ESP_LOGI(TAG, "Connecting to server: %s:%d", server_ip_.c_str(), server_port_);
    int err = connect(socket_fd_, (struct sockaddr *)&dest_addr, sizeof(dest_addr));
    if (err != 0) {
        ESP_LOGE(TAG, "Failed to connect to server: %d", errno);
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }

    ESP_LOGI(TAG, "TCP connection established successfully");
    connected_ = true;

    // 通知连接状态变化
    if (connection_state_callback_) {
        connection_state_callback_(true);
    }

    return true;
}

void Esp8266Client::HandleTcpCommunication() {
    char rx_buffer[128];

    while (!should_stop_) {
        // 创建TCP连接
        if (!CreateTcpConnection()) {
            ESP_LOGE(TAG, "Failed to create TCP connection, retrying in 5 seconds");
            vTaskDelay(5000 / portTICK_PERIOD_MS);
            continue;
        }

        // 发送初始消息
        const char* hello_msg = "Hello from ESP32!";
        if (!SendData(hello_msg)) {
            ESP_LOGE(TAG, "Failed to send initial message");
        }

        // 接收数据循环
        while (!should_stop_ && connected_) {
            int len = recv(socket_fd_, rx_buffer, sizeof(rx_buffer) - 1, 0);

            if (len < 0) {
                ESP_LOGE(TAG, "Receive failed: %d", errno);
                break;
            } else if (len == 0) {
                ESP_LOGI(TAG, "Connection closed by server");
                break;
            } else {
                rx_buffer[len] = 0; // Null-terminate
                ESP_LOGI(TAG, "Received data: %s", rx_buffer);

                // 调用数据接收回调
                if (data_received_callback_) {
                    data_received_callback_(std::string(rx_buffer));
                }
            }
        }

        // 关闭连接
        if (socket_fd_ >= 0) {
            close(socket_fd_);
            socket_fd_ = -1;
        }
        connected_ = false;

        // 通知连接状态变化
        if (connection_state_callback_) {
            connection_state_callback_(false);
        }

        ESP_LOGI(TAG, "TCP connection closed, retrying in 5 seconds");
        vTaskDelay(5000 / portTICK_PERIOD_MS);
    }
}

void Esp8266Client::StartPeriodicConnection(int interval_seconds) {
    if (!initialized_) {
        Initialize();
    }

    periodic_mode_ = true;
    periodic_interval_ = interval_seconds;
    should_stop_ = false;

    ESP_LOGI(TAG, "Starting periodic ESP8266 connection every %d seconds", interval_seconds);

    // 创建定期连接任务
    xTaskCreate(periodic_task, "esp8266_periodic", 4096, this, 5, &periodic_task_handle_);
}

void Esp8266Client::HandlePeriodicConnection() {
    while (!should_stop_) {
        ESP_LOGI(TAG, "Starting periodic connection to ESP8266");

        // 保存当前WiFi配置
        if (SaveCurrentWifiConfig()) {
            // 连接到ESP8266
            if (ConnectToEsp8266()) {
                // 发送数据
                SendData("Periodic hello from ESP32!");

                // 保持连接一小段时间
                vTaskDelay(pdMS_TO_TICKS(5000));

                // 恢复原始WiFi连接
                RestoreOriginalWifi();
            }
        }

        // 等待下次连接
        vTaskDelay(pdMS_TO_TICKS(periodic_interval_ * 1000));
    }
}

bool Esp8266Client::SaveCurrentWifiConfig() {
    esp_err_t ret = esp_wifi_get_config(WIFI_IF_STA, &original_wifi_config_);
    if (ret == ESP_OK) {
        has_original_config_ = true;
        ESP_LOGI(TAG, "Saved current WiFi config: %s", (char*)original_wifi_config_.sta.ssid);
        return true;
    } else {
        ESP_LOGE(TAG, "Failed to save current WiFi config: %s", esp_err_to_name(ret));
        return false;
    }
}

bool Esp8266Client::RestoreOriginalWifi() {
    if (!has_original_config_) {
        ESP_LOGW(TAG, "No original WiFi config to restore");
        return false;
    }

    ESP_LOGI(TAG, "Restoring original WiFi connection: %s", (char*)original_wifi_config_.sta.ssid);

    esp_err_t ret = esp_wifi_set_config(WIFI_IF_STA, &original_wifi_config_);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to restore WiFi config: %s", esp_err_to_name(ret));
        return false;
    }

    ret = esp_wifi_connect();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to reconnect to original WiFi: %s", esp_err_to_name(ret));
        return false;
    }

    return true;
}

void Esp8266Client::periodic_task(void* pvParameters) {
    Esp8266Client* client = static_cast<Esp8266Client*>(pvParameters);
    client->HandlePeriodicConnection();
    vTaskDelete(NULL);
}

void Esp8266Client::tcp_client_task(void* pvParameters) {
    Esp8266Client* client = static_cast<Esp8266Client*>(pvParameters);
    client->HandleTcpCommunication();
    vTaskDelete(NULL);
}

#endif // CONFIG_USE_ESP8266_CLIENT
