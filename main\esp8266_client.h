#ifndef ESP8266_CLIENT_H
#define ESP8266_CLIENT_H

#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <freertos/event_groups.h>
#include <esp_wifi.h>
#include <esp_event.h>
#include <esp_log.h>
#include <lwip/sockets.h>
#include <string>
#include <functional>

class Esp8266Client {
public:
    static Esp8266Client& GetInstance() {
        static Esp8266Client instance;
        return instance;
    }

    // 删除拷贝构造函数和赋值运算符
    Esp8266Client(const Esp8266Client&) = delete;
    Esp8266Client& operator=(const Esp8266Client&) = delete;

    // 初始化ESP8266客户端
    void Initialize();

    // 启动连接到ESP8266（会暂时切换WiFi连接）
    void Start();

    // 停止连接并恢复原WiFi连接
    void Stop();

    // 检查是否已连接
    bool IsConnected() const;

    // 发送数据到ESP8266
    bool SendData(const std::string& data);

    // 发送数据并等待回复
    bool SendDataAndWaitForReply(const std::string& data, int timeout_ms = 10000);

    // 设置数据接收回调
    void OnDataReceived(std::function<void(const std::string&)> callback);

    // 设置连接状态回调
    void OnConnectionStateChanged(std::function<void(bool)> callback);

    // 定期连接ESP8266（每隔一段时间切换连接发送数据）
    void StartPeriodicConnection(int interval_seconds = 60);

private:
    Esp8266Client();
    ~Esp8266Client();

    // WiFi事件处理
    static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                                   int32_t event_id, void* event_data);
    
    // TCP客户端任务
    static void tcp_client_task(void* pvParameters);

    // 定期连接任务
    static void periodic_task(void* pvParameters);

    // 连接到ESP8266 AP
    bool ConnectToEsp8266();

    // 恢复原始WiFi连接
    bool RestoreOriginalWifi();

    // 简单的WiFi重连方法（不依赖事件组）
    bool RestoreOriginalWifiSimple();

    // 检查WiFi连接状态
    bool CheckWifiConnection();

    // 禁用WiFi自动重连
    void DisableWifiAutoReconnect();

    // 启用WiFi自动重连
    void EnableWifiAutoReconnect();

    // 强制断开WiFi并清理状态
    bool ForceDisconnectWifi();

    // 保存当前WiFi配置
    bool SaveCurrentWifiConfig();

    // 创建TCP连接
    bool CreateTcpConnection();

    // 处理TCP通信
    void HandleTcpCommunication();

    // 处理定期连接
    void HandlePeriodicConnection();

    // 测试ESP8266服务器连通性
    bool TestServerConnectivity();

    // 成员变量
    TaskHandle_t tcp_task_handle_;
    TaskHandle_t periodic_task_handle_;
    EventGroupHandle_t wifi_event_group_;
    bool initialized_;
    bool connected_;
    bool should_stop_;
    bool periodic_mode_;
    int socket_fd_;
    int periodic_interval_;

    // 配置参数
    std::string esp8266_ssid_;
    std::string esp8266_password_;
    std::string server_ip_;
    int server_port_;

    // 原始WiFi配置（用于恢复连接）
    wifi_config_t original_wifi_config_;
    bool has_original_config_;

    // 回调函数
    std::function<void(const std::string&)> data_received_callback_;
    std::function<void(bool)> connection_state_callback_;
    
    // WiFi事件位
    static const int WIFI_CONNECTED_BIT = BIT0;
    static const int WIFI_FAIL_BIT = BIT1;
    
    static const char* TAG;
};

#endif // ESP8266_CLIENT_H
