# ESP8266连接调试指南

## 当前问题分析

根据错误日志：
```
E (214073) ESP8266_CLIENT: Failed to connect to server: 113
E (214073) ESP8266_CLIENT: Failed to connect to server: 128
```

- 错误113: `EHOSTUNREACH` - 主机不可达
- 错误128: `ENOTCONN` - 套接字未连接

这些错误表明ESP32虽然连接到了ESP8266的WiFi，但无法建立TCP连接。

## 调试步骤

### 1. 检查ESP8266服务器状态

确保ESP8266端正在运行TCP服务器：

```cpp
// ESP8266端代码检查点
void setup() {
  Serial.begin(115200);
  
  // 设置AP模式
  WiFi.mode(WIFI_AP);
  WiFi.softAP("ESP8266_AP", "password123");
  
  // 确认IP地址
  Serial.print("AP IP: ");
  Serial.println(WiFi.softAPIP()); // 应该显示 ***********
  
  // 启动服务器
  server.begin();
  Serial.println("Server started on port 8080");
}
```

### 2. 验证网络配置

在ESP32端，检查以下配置：

```
CONFIG_ESP8266_SSID="ESP8266_AP"
CONFIG_ESP8266_PASSWORD="password123"
CONFIG_ESP8266_SERVER_IP="***********"
CONFIG_ESP8266_SERVER_PORT=8080
```

### 3. 手动测试连接

可以使用手机或电脑连接到ESP8266_AP热点，然后：

```bash
# 使用telnet测试
telnet *********** 8080

# 或使用nc (netcat)
nc *********** 8080
```

### 4. ESP8266端调试代码

使用以下增强版ESP8266代码进行调试：

```cpp
#include <ESP8266WiFi.h>
#include <WiFiServer.h>

const char* ssid = "ESP8266_AP";
const char* password = "password123";
WiFiServer server(8080);

void setup() {
  Serial.begin(115200);
  Serial.println("\n=== ESP8266 TCP Server Debug ===");
  
  // 设置AP模式
  WiFi.mode(WIFI_AP);
  
  // 配置AP
  IPAddress local_IP(192, 168, 4, 1);
  IPAddress gateway(192, 168, 4, 1);
  IPAddress subnet(255, 255, 255, 0);
  
  WiFi.softAPConfig(local_IP, gateway, subnet);
  bool result = WiFi.softAP(ssid, password);
  
  if (result) {
    Serial.println("AP started successfully");
  } else {
    Serial.println("AP failed to start");
    return;
  }
  
  Serial.print("AP SSID: ");
  Serial.println(ssid);
  Serial.print("AP IP: ");
  Serial.println(WiFi.softAPIP());
  Serial.print("AP MAC: ");
  Serial.println(WiFi.softAPmacAddress());
  
  // 启动服务器
  server.begin();
  Serial.println("TCP Server started on port 8080");
  Serial.println("Waiting for connections...");
}

void loop() {
  // 显示连接的客户端数量
  static unsigned long lastCheck = 0;
  if (millis() - lastCheck > 5000) {
    Serial.print("Connected clients: ");
    Serial.println(WiFi.softAPgetStationNum());
    lastCheck = millis();
  }
  
  // 检查新连接
  WiFiClient client = server.available();
  if (client) {
    Serial.println("\n=== New Client Connected ===");
    Serial.print("Client IP: ");
    Serial.println(client.remoteIP());
    
    // 发送欢迎消息
    client.println("Welcome to ESP8266 Server!");
    
    while (client.connected()) {
      if (client.available()) {
        String data = client.readString();
        data.trim();
        Serial.print("Received: ");
        Serial.println(data);
        
        // 回复消息
        String response = "Echo: " + data + " [" + String(millis()) + "]";
        client.println(response);
        Serial.print("Sent: ");
        Serial.println(response);
      }
      delay(10);
    }
    
    client.stop();
    Serial.println("=== Client Disconnected ===\n");
  }
  
  delay(100);
}
```

### 5. 常见问题和解决方案

#### 问题1: ESP8266 AP没有启动
- 检查ESP8266是否正常上电
- 确认代码已正确上传
- 查看串口输出确认AP状态

#### 问题2: ESP32连接不到ESP8266 WiFi
- 确认SSID和密码正确
- 检查ESP8266 AP是否可见（用手机搜索）
- 确认ESP8266没有设置客户端数量限制

#### 问题3: WiFi连接成功但TCP连接失败
- 确认ESP8266服务器已启动
- 检查IP地址和端口配置
- 确认防火墙没有阻止连接

#### 问题4: 网络配置问题
- ESP8266 AP默认IP: ***********
- 客户端IP范围: ***********-***********54
- 确认ESP32获得了正确的IP地址

### 6. 调试日志分析

正常连接的日志应该是：
```
I ESP8266_CLIENT: Connecting to ESP8266 AP: ESP8266_AP
I ESP8266_CLIENT: Got IP address: ***********
I ESP8266_CLIENT: Current IP: ***********
I ESP8266_CLIENT: Gateway: ***********
I ESP8266_CLIENT: Server connectivity test passed
I ESP8266_CLIENT: Attempting to connect to server: ***********:8080
I ESP8266_CLIENT: TCP connection established successfully
I ESP8266_CLIENT: Sent data: Periodic hello from ESP32!
```

### 7. 临时解决方案

如果问题持续，可以尝试：

1. **增加延迟时间**：在WiFi连接后等待更长时间
2. **简化测试**：先用简单的UDP通信测试
3. **检查硬件**：确认ESP8266和ESP32都工作正常
4. **重启测试**：重启两个设备后重新测试

### 8. 下一步调试

如果以上步骤都没有解决问题，请提供：
1. ESP8266端的完整串口输出
2. ESP32端的完整日志
3. 使用的具体硬件型号
4. 网络环境信息
