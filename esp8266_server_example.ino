/*
 * ESP8266 TCP服务器示例
 * 用于与ESP32客户端通信
 * 
 * 硬件要求：ESP8266开发板（如NodeMCU、Wemos D1 Mini等）
 * 
 * 功能：
 * 1. 创建WiFi热点（AP模式）
 * 2. 启动TCP服务器监听8080端口
 * 3. 接收ESP32发送的数据
 * 4. 发送响应数据给ESP32
 */

#include <ESP8266WiFi.h>
#include <WiFiServer.h>

// WiFi配置
const char* ssid = "ESP8266_AP";
const char* password = "password123";

// TCP服务器配置
WiFiServer server(8080);

void setup() {
  Serial.begin(115200);
  Serial.println();
  Serial.println("ESP8266 TCP Server Starting...");
  
  // 设置为AP模式
  WiFi.mode(WIFI_AP);
  
  // 配置AP参数
  IPAddress local_IP(192, 168, 4, 1);
  IPAddress gateway(192, 168, 4, 1);
  IPAddress subnet(255, 255, 255, 0);
  
  WiFi.softAPConfig(local_IP, gateway, subnet);
  WiFi.softAP(ssid, password);
  
  Serial.println("WiFi AP started");
  Serial.print("AP SSID: ");
  Serial.println(ssid);
  Serial.print("AP IP address: ");
  Serial.println(WiFi.softAPIP());
  
  // 启动TCP服务器
  server.begin();
  Serial.println("TCP Server started on port 8080");
  Serial.println("Waiting for ESP32 connections...");
}

void loop() {
  // 检查是否有客户端连接
  WiFiClient client = server.available();
  
  if (client) {
    Serial.println("=== New client connected ===");
    Serial.print("Client IP: ");
    Serial.println(client.remoteIP());
    
    // 处理客户端连接
    while (client.connected()) {
      if (client.available()) {
        // 读取客户端发送的数据
        String receivedData = client.readString();
        receivedData.trim(); // 移除换行符
        
        Serial.print("Received from ESP32: ");
        Serial.println(receivedData);
        
        // 构造响应消息
        String response = "Hello from ESP8266! ";
        response += "Received: " + receivedData;
        response += " | Time: " + String(millis());
        
        // 发送响应给客户端
        client.println(response);
        Serial.print("Sent response: ");
        Serial.println(response);
        
        // 可以在这里添加更多的数据处理逻辑
        // 例如：控制LED、读取传感器数据等
        
        // 示例：根据接收到的命令执行不同操作
        if (receivedData.indexOf("LED_ON") >= 0) {
          digitalWrite(LED_BUILTIN, LOW);  // ESP8266内置LED低电平点亮
          client.println("LED turned ON");
          Serial.println("LED turned ON");
        } else if (receivedData.indexOf("LED_OFF") >= 0) {
          digitalWrite(LED_BUILTIN, HIGH); // ESP8266内置LED高电平熄灭
          client.println("LED turned OFF");
          Serial.println("LED turned OFF");
        } else if (receivedData.indexOf("STATUS") >= 0) {
          String status = "ESP8266 Status: ";
          status += "Uptime: " + String(millis() / 1000) + "s, ";
          status += "Free Heap: " + String(ESP.getFreeHeap()) + " bytes, ";
          status += "Connected Clients: " + String(WiFi.softAPgetStationNum());
          client.println(status);
          Serial.println("Sent status: " + status);
        }
      }
      delay(10); // 小延迟避免CPU占用过高
    }
    
    // 客户端断开连接
    client.stop();
    Serial.println("=== Client disconnected ===");
  }
  
  delay(100); // 主循环延迟
}

/*
 * 扩展功能示例：
 * 
 * 1. 添加传感器读取：
 *    - 温湿度传感器（DHT22）
 *    - 光照传感器
 *    - 运动检测传感器
 * 
 * 2. 添加执行器控制：
 *    - 继电器控制
 *    - 舵机控制
 *    - PWM输出
 * 
 * 3. 数据存储：
 *    - EEPROM存储配置
 *    - 文件系统存储日志
 * 
 * 4. 网络功能：
 *    - 同时支持STA和AP模式
 *    - 网络时间同步
 *    - OTA更新
 */

/*
 * 使用说明：
 * 
 * 1. 将此代码上传到ESP8266开发板
 * 2. 打开串口监视器（波特率115200）
 * 3. 确认ESP8266创建了名为"ESP8266_AP"的WiFi热点
 * 4. 在ESP32端启用ESP8266客户端功能
 * 5. 观察串口输出，查看通信过程
 * 
 * 测试命令：
 * - ESP32发送"LED_ON"：点亮ESP8266内置LED
 * - ESP32发送"LED_OFF"：熄灭ESP8266内置LED  
 * - ESP32发送"STATUS"：获取ESP8266状态信息
 */
